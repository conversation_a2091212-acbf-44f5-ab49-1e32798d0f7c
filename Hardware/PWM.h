#ifndef __PWM_H
#define __PWM_H

#include "main.h"

// �ٶȺ�ʱ�����
#define STEPPER_Speed        3000     // ����ٶȣ�Hz��
#define STEPPER_Duty  		   10      // �����ȣ�%��

// ������غ궨��
#define DECEL_DISTANCE 1000        	// ���پ��루��������
#define MIN_STEP_HZ	   200          // ��Ͳ���Ƶ�ʣ�Hz������������������ж�

// ��������֤�궨��
#define STEPPER_MAX_SPEED_LIMIT 5000     // ���ٶ����ƣ�Hz��
#define STEPPER_MIN_SPEED_LIMIT 100      // ��С�ٶ����ƣ�Hz��
#define STEPPER_MAX_DECEL_DISTANCE 2000  // ���پ������ƣ�������

// ���������ṹ��
typedef struct
{
    uint16_t max_speed;      // ���ٶȣ�Hz��
    uint16_t min_speed;      // ��С�ٶȣ�Hz��
    uint16_t acceleration;   // ���ٶȣ�Hz/s��
    uint16_t decel_distance; // ���پ��루������
    uint8_t  duty_cycle;     // PWMռ�ձȣ�%��
} StepperConfig_t;

// ��������
void Stepper_Init(void);
void Stepper_SetPosition_1(int32_t position);  // ���õ��1Ŀ��λ��
void Stepper_SetPosition_2(int32_t position);  // ���õ��2Ŀ��λ��

// ���ù���API
void Stepper_SetConfig(uint8_t motor_id, const StepperConfig_t* config);  // ���õ������
void Stepper_GetConfig(uint8_t motor_id, StepperConfig_t* config);        // ��ȡ������

#endif

