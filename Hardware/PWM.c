#include "PWM.h"

// ��������� - ��Ϊ�з�����
volatile int32_t Stepper1_PulseCount = 0;
volatile int32_t Stepper2_PulseCount = 0;

//Ԥ������
volatile int32_t Stepper1_Expectedpulse = 0;
volatile int32_t Stepper2_Expectedpulse = 0;


/**
 * @brief  ��ʼ��TIM2��TIM3��CH1 PWMͨ������ʼ���������GPIO
 * @param  None
 * @retval None
 */
void Stepper_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
    TIM_OCInitTypeDef TIM_OCInitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;
    
    // ʹ��ʱ��
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2 | RCC_APB1Periph_TIM3, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
    
    // ����GPIO - TIM2_CH1(PA0), TIM3_CH1(PA6)
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_6;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
	
	// ����PA1��PA7Ϊ������� -- ���Ʒ���
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_1 | GPIO_Pin_7;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // Ĭ������Ϊ�ߵ�ƽ
    GPIO_SetBits(GPIOA, GPIO_Pin_1 | GPIO_Pin_7);
    
    // ���㶨ʱ������ (����ϵͳʱ��72MHz)
    uint16_t prescaler = 72 - 1;  // 1MHz����Ƶ��
    uint16_t period = 1000000 / STEPPER_Speed - 1;
    uint16_t pulse = (period + 1) * STEPPER_Duty / 100;
    
    // TIM2����
    TIM_TimeBaseStructure.TIM_Period = period;
    TIM_TimeBaseStructure.TIM_Prescaler = prescaler;
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(TIM2, &TIM_TimeBaseStructure);
    
    TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;
    TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;
    TIM_OCInitStructure.TIM_Pulse = pulse;
    TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High;
    TIM_OC1Init(TIM2, &TIM_OCInitStructure);
    
    // TIM3����
    TIM_TimeBaseInit(TIM3, &TIM_TimeBaseStructure);
    TIM_OC1Init(TIM3, &TIM_OCInitStructure);
    
    // �����ж�
    NVIC_InitStructure.NVIC_IRQChannel = TIM2_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    
    NVIC_InitStructure.NVIC_IRQChannel = TIM3_IRQn;
    NVIC_Init(&NVIC_InitStructure);
    
    // ʹ�ܸ����ж�
    TIM_ITConfig(TIM2, TIM_IT_Update, ENABLE);
    TIM_ITConfig(TIM3, TIM_IT_Update, ENABLE);
    
    // Ĭ�Ϲرն�ʱ��
    TIM_Cmd(TIM2, DISABLE);
    TIM_Cmd(TIM3, DISABLE);   
}

/**
 * @brief  ����PWMƵ��
 * @param  TIMx: ��ʱ�� (TIM2 �� TIM3)
 * @param  frequency: �µ�Ƶ�� (Hz)
 * @retval None
 */
void PWM_SetFrequency(TIM_TypeDef* TIMx, uint16_t frequency)
{
    uint16_t period = 1000000 / frequency - 1;
    TIM_SetAutoreload(TIMx, period);
    
    // ���ֵ�ǰռ�ձ�
    uint16_t current_pulse = TIM_GetCapture1(TIMx);
    uint16_t current_period = TIMx->ARR;
    uint16_t new_pulse = (uint32_t)current_pulse * (period + 1) / (current_period + 1);
    TIM_SetCompare1(TIMx, new_pulse);
}





/**
 * @brief  ���õ��1Ŀ��λ��
 * @param  position: Ŀ��λ�ã����������������ţ�
 * @retval None
 */
void Stepper_SetPosition_1(int32_t position)
{
	Stepper1_Expectedpulse = position;
	
	//������岻һ���ʹ򿪶�ʱ���������
	if(Stepper1_Expectedpulse != Stepper1_PulseCount)
	{
		TIM_Cmd(TIM2, ENABLE);
	
	}
	
}


/**
 * @brief  ���õ��2Ŀ��λ��
 * @param  position: Ŀ��λ�ã����������������ţ�
 * @retval None
 */
void Stepper_SetPosition_2(int32_t position)
{
	Stepper2_Expectedpulse = position;
	
	//������岻һ���ʹ򿪶�ʱ���������
	if(Stepper2_Expectedpulse != Stepper2_PulseCount)
	{
		TIM_Cmd(TIM3, ENABLE);
	
	}
	
}


/**
 * @brief  ��¼���²�������
 * @param  None
 * @retval None
 */
static void Generate_Step_Pulse_1(void)
{
	// ��ȡPA1��ƽ״̬���ߵ�ƽ�Ӽӣ��͵�ƽ����
	if(GPIO_ReadOutputDataBit(GPIOA, GPIO_Pin_1))
		Stepper1_PulseCount++;
	else
		Stepper1_PulseCount--;
	
    // ���㵱ǰ��Ŀ��ľ��� - ���ж�ֹͣ
    int32_t distance =Stepper1_Expectedpulse - Stepper1_PulseCount;
	if(distance >0)
	{
		GPIO_SetBits(GPIOA, GPIO_Pin_1);
	}
	else if(distance <0)
	{
		GPIO_ResetBits(GPIOA, GPIO_Pin_1);
		
		distance = -distance;
	}
	else
	{
		//���û������ֱ��ֹͣPWM���
		TIM_Cmd(TIM2, DISABLE);
		return ;
	}
	
	//������һ�������Ƶ��
	if(distance >= DECEL_DISTANCE)
	{
		//���û�����پ����ȫ����
		PWM_SetFrequency(TIM2,STEPPER_Speed);
	}
	else
	{
		PWM_SetFrequency(TIM2,((STEPPER_Speed - MIN_STEP_HZ)/DECEL_DISTANCE)*distance + MIN_STEP_HZ);
	}	
}

static void Generate_Step_Pulse_2(void)
{
	// ��ȡPA7��ƽ״̬���ߵ�ƽ�Ӽӣ��͵�ƽ����
	if(GPIO_ReadOutputDataBit(GPIOA, GPIO_Pin_7))
		Stepper2_PulseCount++;
	else
		Stepper2_PulseCount--;
	
    // ���㵱ǰ��Ŀ��ľ��� - ���ж�ֹͣ
    int32_t distance = Stepper2_Expectedpulse - Stepper2_PulseCount;
	if(distance >0)
	{
		GPIO_SetBits(GPIOA, GPIO_Pin_7);
	}
	else if(distance <0)
	{
		GPIO_ResetBits(GPIOA, GPIO_Pin_7);
		
		distance = -distance;
	}
	else
	{
		//���û������ֱ��ֹͣPWM���
		TIM_Cmd(TIM3, DISABLE);
		return ;
	}
	
	//������һ�������Ƶ��
	if(distance >= DECEL_DISTANCE)
	{
		//���û�����پ����ȫ����
		PWM_SetFrequency(TIM3,STEPPER_Speed);
	}
	else
	{
		PWM_SetFrequency(TIM3,((STEPPER_Speed - MIN_STEP_HZ)/DECEL_DISTANCE)*distance + MIN_STEP_HZ);
	}	
}


/****************************************************************************
��	�ã��жϷ�����

��	������

����ֵ����
****************************************************************************/
void TIM2_IRQHandler(void)
{
    if(TIM_GetITStatus(TIM2, TIM_IT_Update) != RESET)
    {
        
		Generate_Step_Pulse_1();
		
        TIM_ClearITPendingBit(TIM2, TIM_IT_Update);
    }
}

void TIM3_IRQHandler(void)
{
    if(TIM_GetITStatus(TIM3, TIM_IT_Update) != RESET)
    {
        Generate_Step_Pulse_2();
		
		
        TIM_ClearITPendingBit(TIM3, TIM_IT_Update);
    }
}


